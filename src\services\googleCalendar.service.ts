import { CalendarEventDao } from "../lib/dao/calendarEvent.dao";
import { GoogleCalendarDao } from "../lib/dao/googleCalendar.dao";
import { google } from "googleapis";
import { throwError } from "../util/response";
import { CONFIG } from "../config/environment";
import { TherapistDao } from "../lib/dao/therapist.dao";
import { v4 as uuidv4 } from "uuid";
import { Utility } from "../util/util";
import { ScheduleDao } from "../lib/dao/schedule.dao";
import { ClientService } from "./client.service";
import moment from "moment";
import ScheduleModel, { ScheduleStatus } from "../models/Schedule.model";

import { Types } from "mongoose";
import { PayTrackerService } from "./payTracker.service";
import {
  PaymentTrackerStatusEnum,
  PaymentTrackerTypeEnum,
} from "../models/PayTracker.model";
import calendarEventModel from "../models/calendarEvent.model";
import createScheduleTemplate from "../util/emailTemplate/create.schedule";
import { Mailer2 } from "../util/mailer2";
import { ScheduleService } from "./schedule.service";
import { isOverlap, findOverlaps } from "../helper/custom.helper";
import { MailSubjectEnum } from "../lib/enum/subject.enum";

export interface IAddEventPayload {
  emails: string[];
  summary: string;
  location: string;
  description: string;
}

export class GoogleCalendarService {
  static async create(payload: any) {
    return await GoogleCalendarDao.create(payload);
  }

  static async update(_id: any, payload: any) {
    return await GoogleCalendarDao.update(_id, payload);
  }

  static async findByTherapist(therapist: any) {
    return await GoogleCalendarDao.findByTherapist(therapist);
  }

  static async findby_id(_id: any) {
    return await CalendarEventDao.findby_id(_id);
  }

  static async createEvent(event: any) {
    return await GoogleCalendarDao.createEvent(event);
  }

  static async CheckEventLocally(eventIds: any) {
    const ids = await CalendarEventDao.CheckEventLocally(eventIds);
    const eventId = ids.map((data: any) => {
      return data.id;
    });
    return eventId;
  }

  static async addEventToCalender(
    therapistId: any,
    data: IAddEventPayload,
    recurrenceDate: {
      toDate: any;
      fromDate: any;
      _id: any;
      rrule?: any;
      data?: any;
    },
    scheduleId: any
  ) {
    const googleCalendarData = await GoogleCalendarService.findByTherapist(
      therapistId
    );
    const therapist: any = await TherapistDao.getTherapist(therapistId);

    if (!googleCalendarData) {
      return throwError("No Google Calender Data found.", 404);
    }

    const oauth2Client = new google.auth.OAuth2(
      CONFIG.clientId,
      CONFIG.clientSecret,
      CONFIG.clientRedirectUrl
    );

    const tokens = {
      access_token: googleCalendarData.access_token,
      refresh_token: googleCalendarData.refresh_token, // you'll only get this once, so store it securely
    };

    // Assuming you're storing tokens in session after authentication
    if (tokens) {
      oauth2Client.setCredentials(tokens);

      const calendar: any = google.calendar({
        version: "v3",
        auth: oauth2Client,
      });
      const emails = data.emails.map((email: string) => {
        return {
          email: email,
        };
      });
      emails.push({
        email: therapist.email,
      });
      const finalLinkData: any = {};
      // const recurrenceDate: any = await Utility.findNearestFromDate(schedule);
      // finalLinkData.recurrenceDate = recurrenceDate;
      const event = {
        summary: data.summary,
        location: data.location,
        description: data.description,
        start: {
          dateTime: recurrenceDate.fromDate,
          timeZone: "IST",
        },
        end: {
          dateTime: recurrenceDate.toDate,
          timeZone: "IST",
        },
        attendees: emails, // [{ email: '<EMAIL>' }]
        recurrence: recurrenceDate.rrule ? [recurrenceDate.rrule] : [],
        conferenceData: {
          createRequest: {
            requestId: uuidv4(), // Generate a random string for the requestId
            conferenceSolutionKey: {
              type: "hangoutsMeet", // Use 'hangoutsMeet' for Google Meet
            },
          },
        },
      };
      // Check if this is a recurring event
      const isRecurring =
        recurrenceDate.rrule && recurrenceDate.rrule.length > 0;

      // Create the event in Google Calendar
      const createdEvent = await calendar.events.insert({
        calendarId: "primary",
        resource: event,
        conferenceDataVersion: 1,
        requestBody: event,
      });

      finalLinkData.link = createdEvent.data.hangoutLink;

      if (
        isRecurring &&
        recurrenceDate.data &&
        recurrenceDate.data.length > 0
      ) {
        try {
          // Wait a moment for Google Calendar to process the recurring event
          await new Promise((resolve) => setTimeout(resolve, 2000));

          // Try to get instances of the recurring event
          const instances = await calendar.events.instances({
            calendarId: "primary",
            eventId: createdEvent.data.id,
          });

          if (instances.data.items && instances.data.items.length > 0) {
            // Create an array to store all calendar events
            const calendarEvents = [];

            // Process each instance and match it with a recurrence date
            for (let i = 0; i < instances.data.items.length; i++) {
              const instance = instances.data.items[i];

              // Find the matching recurrence date by comparing dates
              const instanceDate = moment(instance.start.dateTime).format(
                "YYYY-MM-DD"
              );

              // Find the matching recurrence date
              const matchingRecDate = recurrenceDate.data.find((rd: any) => {
                const rdDate = moment(rd.fromDate).format("YYYY-MM-DD");
                return rdDate === instanceDate;
              });

              // If we found a matching recurrence date, use its ID
              // Otherwise, use the ID of the recurrence date at the same index (or the first one if out of bounds)
              const scheduleRecId = matchingRecDate
                ? matchingRecDate._id
                : recurrenceDate.data[i]
                ? recurrenceDate.data[i]._id
                : recurrenceDate.data[0]._id;

              // Create the calendar event in the database
              const calendarEvent = await GoogleCalendarDao.createEvent({
                ...instance,
                scheduleId: scheduleId,
                therapistId: therapistId,
                scheduleRecId: scheduleRecId,
              });

              calendarEvents.push(calendarEvent);
            }

            // If we didn't get enough instances, create individual events for the missing dates
            if (instances.data.items.length < recurrenceDate.data.length) {
              // Find which recurrence dates don't have matching instances
              const processedDates = new Set(
                instances.data.items.map((item: any) =>
                  moment(item.start.dateTime).format("YYYY-MM-DD")
                )
              );

              const missingDates = recurrenceDate.data.filter(
                (rd: any) =>
                  !processedDates.has(moment(rd.fromDate).format("YYYY-MM-DD"))
              );

              // Create individual events for each missing date
              for (const rd of missingDates) {
                // Create a new event with the same details but different date
                const individualEvent = {
                  summary: event.summary,
                  location: event.location,
                  description: event.description,
                  start: {
                    dateTime: rd.fromDate,
                    timeZone: "IST",
                  },
                  end: {
                    dateTime: rd.toDate,
                    timeZone: "IST",
                  },
                  attendees: event.attendees,
                  conferenceData: {
                    createRequest: {
                      requestId: uuidv4(),
                      conferenceSolutionKey: {
                        type: "hangoutsMeet",
                      },
                    },
                  },
                };

                // Create the event in Google Calendar
                const individualCreatedEvent = await calendar.events.insert({
                  calendarId: "primary",
                  resource: individualEvent,
                  conferenceDataVersion: 1,
                  requestBody: individualEvent,
                });

                // Save the event in the database
                const individualCalendarEvent =
                  await GoogleCalendarDao.createEvent({
                    ...individualCreatedEvent.data,
                    scheduleId: scheduleId,
                    therapistId: therapistId,
                    scheduleRecId: rd._id,
                  });

                calendarEvents.push(individualCalendarEvent);
              }
            }

            finalLinkData.calenderEventId = calendarEvents;
          } else {
            // Create an array to store all calendar events
            const calendarEvents = [];

            // First, save the master event
            const masterEvent = await GoogleCalendarDao.createEvent({
              ...createdEvent.data,
              scheduleId: scheduleId,
              therapistId: therapistId,
              scheduleRecId: recurrenceDate.data[0]._id,
            });

            calendarEvents.push(masterEvent);

            // Create individual events for each recurrence date (except the first one which is already created)
            for (let i = 1; i < recurrenceDate.data.length; i++) {
              const rd = recurrenceDate.data[i];

              // Create a new event with the same details but different date
              const individualEvent = {
                summary: event.summary,
                location: event.location,
                description: event.description,
                start: {
                  dateTime: rd.fromDate,
                  timeZone: "IST",
                },
                end: {
                  dateTime: rd.toDate,
                  timeZone: "IST",
                },
                attendees: event.attendees,
                conferenceData: {
                  createRequest: {
                    requestId: uuidv4(),
                    conferenceSolutionKey: {
                      type: "hangoutsMeet",
                    },
                  },
                },
              };

              // Create the event in Google Calendar
              const individualCreatedEvent = await calendar.events.insert({
                calendarId: "primary",
                resource: individualEvent,
                conferenceDataVersion: 1,
                requestBody: individualEvent,
              });

              // Save the event in the database
              const individualCalendarEvent =
                await GoogleCalendarDao.createEvent({
                  ...individualCreatedEvent.data,
                  scheduleId: scheduleId,
                  therapistId: therapistId,
                  scheduleRecId: rd._id,
                });

              calendarEvents.push(individualCalendarEvent);
            }

            finalLinkData.calenderEventId = calendarEvents;
          }
        } catch (error) {
          console.error("ERROR: Error handling recurring event:", error);

          // Create an array to store all calendar events
          const calendarEvents = [];

          // First, save the master event
          const masterEvent = await GoogleCalendarDao.createEvent({
            ...createdEvent.data,
            scheduleId: scheduleId,
            therapistId: therapistId,
            scheduleRecId: recurrenceDate.data[0]._id,
          });

          calendarEvents.push(masterEvent);

          // Create individual events for each recurrence date (except the first one which is already created)
          for (let i = 1; i < recurrenceDate.data.length; i++) {
            const rd = recurrenceDate.data[i];

            // Create a new event with the same details but different date
            const individualEvent = {
              summary: event.summary,
              location: event.location,
              description: event.description,
              start: {
                dateTime: rd.fromDate,
                timeZone: "IST",
              },
              end: {
                dateTime: rd.toDate,
                timeZone: "IST",
              },
              attendees: event.attendees,
              conferenceData: {
                createRequest: {
                  requestId: uuidv4(),
                  conferenceSolutionKey: {
                    type: "hangoutsMeet",
                  },
                },
              },
            };

            // Create the event in Google Calendar
            const individualCreatedEvent = await calendar.events.insert({
              calendarId: "primary",
              resource: individualEvent,
              conferenceDataVersion: 1,
              requestBody: individualEvent,
            });

            // Save the event in the database
            const individualCalendarEvent = await GoogleCalendarDao.createEvent(
              {
                ...individualCreatedEvent.data,
                scheduleId: scheduleId,
                therapistId: therapistId,
                scheduleRecId: rd._id,
              }
            );

            calendarEvents.push(individualCalendarEvent);
          }

          finalLinkData.calenderEventId = calendarEvents;
        }
      } else {
        const calenderEvent = await GoogleCalendarDao.createEvent({
          ...createdEvent.data,
          scheduleId: scheduleId,
          therapistId: therapistId,
          scheduleRecId: recurrenceDate._id,
        });
        finalLinkData.calenderEventId = [calenderEvent];
      }

      return finalLinkData;
    } else {
      throw new Error("Authentication tokens are not available");
    }
  }

  static async addSingleEventToCalender(
    therapistId: any,
    data: IAddEventPayload,
    recurrenceDate: {
      toDate: any;
      fromDate: any;
      _id: any;
      rrule?: any;
      data?: any;
    },
    scheduleId: any
  ) {
    const googleCalendarData = await GoogleCalendarService.findByTherapist(
      therapistId
    );
    const therapist: any = await TherapistDao.getTherapist(therapistId);

    if (!googleCalendarData) {
      return throwError("No Google Calender Data found.", 404);
    }

    const oauth2Client = new google.auth.OAuth2(
      CONFIG.clientId,
      CONFIG.clientSecret,
      CONFIG.clientRedirectUrl
    );

    const tokens = {
      access_token: googleCalendarData.access_token,
      refresh_token: googleCalendarData.refresh_token, // you'll only get this once, so store it securely
    };

    // Assuming you're storing tokens in session after authentication
    if (tokens) {
      oauth2Client.setCredentials(tokens);

      const calendar: any = google.calendar({
        version: "v3",
        auth: oauth2Client,
      });
      const emails = data.emails.map((email: string) => {
        return {
          email: email,
        };
      });
      emails.push({
        email: therapist.email,
      });
      const finalLinkData: any = {};
      // const recurrenceDate: any = await Utility.findNearestFromDate(schedule);
      // finalLinkData.recurrenceDate = recurrenceDate;
      const event = {
        summary: data.summary,
        location: data.location,
        description: data.description,
        start: {
          dateTime: recurrenceDate.fromDate,
          timeZone: "IST",
        },
        end: {
          dateTime: recurrenceDate.toDate,
          timeZone: "IST",
        },
        attendees: emails, // [{ email: '<EMAIL>' }]
        recurrence: recurrenceDate.rrule ? [recurrenceDate.rrule] : [],
        conferenceData: {
          createRequest: {
            requestId: uuidv4(), // Generate a random string for the requestId
            conferenceSolutionKey: {
              type: "hangoutsMeet", // Use 'hangoutsMeet' for Google Meet
            },
          },
        },
      };
      const createdEvent = await calendar.events.insert({
        calendarId: "primary",
        resource: event,
        conferenceDataVersion: 1, // Required to include conferenceData
        // sendNotifications: true, // If you want to send email notifications to attendees
        requestBody: event, // The event details
      });
      finalLinkData.link = createdEvent.data.hangoutLink;

      // const calenderEvent = await GoogleCalendarDao.createEvent(createEventArr);
      const calenderEvent = await GoogleCalendarDao.createEvent({
        ...createdEvent.data,
        scheduleId: scheduleId,
        therapistId: therapistId,
        scheduleRecId: recurrenceDate._id,
      });
      finalLinkData.calenderEventId = calenderEvent._id;

      return finalLinkData;
    } else {
      throw new Error("Authentication tokens are not available");
    }
  }

  static async getEventById(eventId: any) {
    return await CalendarEventDao.getEventById(eventId);
  }

  static async removeEventFromCalendar(
    therapistId: any,
    recurrenceData: any,
    deleteEntireSeries: boolean = false
  ) {
    const googleCalendarData = await GoogleCalendarService.findByTherapist(
      therapistId
    );
    const calendarEvent = await CalendarEventDao.findByTherapistAndRecId(
      therapistId,
      recurrenceData._id,
      recurrenceData.calenderEventId
    );

    // Remove the event from recurrence in the database
    await ScheduleDao.removeEventFromRecurrence(
      therapistId,
      recurrenceData._id
    );

    if (!googleCalendarData) {
      return throwError("No Google Calender Data found.", 404);
    }

    const oauth2Client = new google.auth.OAuth2(
      CONFIG.clientId,
      CONFIG.clientSecret,
      CONFIG.clientRedirectUrl
    );

    const tokens = {
      access_token: googleCalendarData.access_token,
      refresh_token: googleCalendarData.refresh_token, // you'll only get this once, so store it securely
    };

    // Assuming you're storing tokens in session after authentication
    if (tokens) {
      oauth2Client.setCredentials(tokens);

      const calendar: any = google.calendar({
        version: "v3",
        auth: oauth2Client,
      });
      // await calendar.events.delete({
      //   calendarId: "primary",
      //   eventId: calendarEvent?.id, // Replace with the ID of the event you want to delete.
      // });
      try {
        if (deleteEntireSeries) {
          const calenderEventData = await GoogleCalendarService.getEventById(
            recurrenceData.calenderEventId
          );

          if (!calenderEventData) {
            console.warn(`Calendar event data not found for ID: ${recurrenceData.calenderEventId}`);
            return; // Don't throw error, just return
          }

          // If it's a recurring event instance, use the recurringEventId (master event id)
          // Otherwise use the current event id (it might be the master event already)
          const masterEventId = calenderEventData?.id.split("_")[0];

          try {
            await calendar.events.delete({
              calendarId: "primary",
              eventId: masterEventId,
            });
            console.log(`Successfully deleted entire series: ${masterEventId}`);
          } catch (deleteError: any) {
            if (deleteError.response?.status === 404) {
              console.warn(`Event ${masterEventId} not found in Google Calendar (already deleted)`);
            } else if (deleteError.response?.status === 400) {
              console.warn(`Bad request when deleting event ${masterEventId}: ${deleteError.message}`);
            } else {
              console.error(`Error deleting entire series ${masterEventId}:`, deleteError.message);
              throw deleteError;
            }
          }
        } else {
          // Delete single instance
          if (!calendarEvent?.id) {
            console.warn(`No calendar event ID found for recurrence: ${recurrenceData._id}`);
            return;
          }

          try {
            await calendar.events.delete({
              calendarId: "primary",
              eventId: calendarEvent.id,
            });
            console.log(`Successfully deleted single event: ${calendarEvent.id}`);
          } catch (deleteError: any) {
            if (deleteError.response?.status === 404) {
              console.warn(`Event ${calendarEvent.id} not found in Google Calendar (already deleted)`);
            } else if (deleteError.response?.status === 400) {
              console.warn(`Bad request when deleting event ${calendarEvent.id}: ${deleteError.message}`);
            } else {
              console.error(`Error deleting single event ${calendarEvent.id}:`, deleteError.message);
              throw deleteError;
            }
          }
        }
      } catch (error: any) {
        console.error("Error in removeEventFromCalendar:", error.message);
        // Don't re-throw the error to prevent breaking the flow
        // The database cleanup has already happened above
      }
    } else {
      throw new Error("Authentication tokens are not available");
    }
  }

  static async syncCalendarLocally(therapistId: any, eventIds: any) {
    const googleCalendarData = await GoogleCalendarService.findByTherapist(
      therapistId
    );
    if (!googleCalendarData) {
      return throwError("No Google Calender Data found.", 404);
    }

    const old_schedules = await ScheduleService.getScheduleByTherapistId(
      therapistId
    );
    let existing_dates = [];
    for (const old_schedule of old_schedules) {
      for (const old_date of old_schedule.recurrenceDates) {
        if (old_date.status != ScheduleStatus.CANCELLED) {
          existing_dates.push({
            fromDate: moment(old_date.fromDate),
            toDate: moment(old_date.toDate),
          });
        }
      }
    }

    const therapist = await TherapistDao.getTherapist(therapistId);
    if (!therapist) {
      return throwError("No Therapist found.", 404);
    }

    const oauth2Client = new google.auth.OAuth2(
      CONFIG.clientId,
      CONFIG.clientSecret,
      CONFIG.clientRedirectUrl
    );

    const tokens = {
      access_token: googleCalendarData.access_token,
      refresh_token: googleCalendarData.refresh_token, // you'll only get this once, so store it securely
    };

    if (!tokens) {
      return throwError("No Google Calender Data found.", 404);
    }
    oauth2Client.setCredentials(tokens);

    let existing_events = await CalendarEventDao.getTherapistCalendarEvents(
      therapistId
    );

    let existing_event_ids = existing_events.map((event) => event.id);

    let not_existing_ids = eventIds.filter(
      (id: any) => !existing_event_ids.includes(id)
    );

    if (not_existing_ids.length == 0) {
      return throwError("All events are already synced.", 404);
    }

    const calendar = google.calendar({ version: "v3", auth: oauth2Client });

    const events = await Promise.all(
      not_existing_ids.map(async (eventId: any) => {
        const calendarResponse: any = await calendar.events.get({
          calendarId: "primary",
          eventId: eventId,
        });
        calendarResponse.data.therapistId = therapistId;
        return calendarResponse.data;
      })
    );

    const all_uids = events.map((event) => event.iCalUID);
    const all_uids_unique = Array.from(new Set(all_uids));

    let schedules: any = [];
    let timeZone;
    let name;
    let email;
    for (let uid of all_uids_unique) {
      const uid_events = events.filter((event) => event.iCalUID == uid);
      const first_event_data = uid_events[0];

      const clientEmails = await first_event_data.attendees
        .filter(
          (attendee: any) => String(attendee.email) != String(therapist.email)
        )
        .map((attendee: any) => String(attendee.email));
      const uniqueClientEmails = Array.from(new Set(clientEmails));

      let client: any = undefined;
      for (let email of uniqueClientEmails) {
        const clientExist = await ClientService.getClientByEmail(
          String(email),
          therapistId
        );
        if (clientExist) {
          client = clientExist;
          break;
        }
      }

      if (!client) {
        client = await ClientService.create({
          email: uniqueClientEmails[0],
          therapistId: therapistId,
        });
      }

      timeZone = client?.defaultTimezone || "Asia/Kolkata";
      name = client?.name || "patient";
      email = client?.email;

      if (!client) {
        return throwError("client not created. Something went wrong.", 404);
      }
      const additionalEmails = uniqueClientEmails.filter(
        (email: any) => email != client.email
      );
      const duration = moment(first_event_data.end.dateTime).diff(
        moment(first_event_data.start.dateTime),
        "minutes"
      );

      let tillDate = moment(first_event_data.start.dateTime);
      tillDate = tillDate.add(3, "months");

      let scheduleData = new ScheduleModel({
        therapistId: therapist._id,
        recurrenceDates: [],
        scheduleId: therapist.identifier + "_" + Utility.generateRandomNumber(),
        clientId: client._id,
        additionalEmails: additionalEmails,
        durationOfSchedule: duration,
        // location: first_event_data.location,
        location: "online",
        clientCountry: "India",
        email: client.email,
        name: client?.name || "",
        phone: client?.phone || "",
        age: client?.age || "",
        gender: client?.gender || "",
        tillDate: tillDate || "",
        description: first_event_data.description
          ? first_event_data.description
          : first_event_data.summary,
        summary: first_event_data.summary
          ? first_event_data.summary
          : first_event_data.description,
      });

      // console.log(uid_events);

      for (let uid_event of uid_events) {
        const recurrenceDate = [
          {
            fromDate: new Date(uid_event.start.dateTime),
            toDate: new Date(uid_event.end.dateTime),
          },
        ];
        const checkifOverlap = isOverlap(recurrenceDate, existing_dates);

        if (checkifOverlap) {
          // Find all conflicts
          const conflicts = findOverlaps(recurrenceDate, existing_dates);

          // Format the first conflict for the message
          const firstConflict = conflicts[0];
          const conflictDate = moment(firstConflict.startTime).format(
            "YYYY-MM-DD h:mm A"
          );

          let errorMessage = `A conflict was found. You have ${
            conflicts.length
          } existing session${
            conflicts.length > 1 ? "s" : ""
          } in your calendar.`;

          // Add the first conflict date as an example
          errorMessage += ` For example, you have a session on ${conflictDate}.`;

          const error: any = new Error(errorMessage);
          error.status = 400;
          error.conflicts = conflicts;
          throw error;
        }

        const saveEvent = await GoogleCalendarService.createEvent(uid_event);
        if (!saveEvent) {
          return throwError("Calendar Event No Saved In DataBase.", 500);
        }
        const reccurrance: any = {
          fromDate: new Date(uid_event.start.dateTime),
          toDate: new Date(uid_event.end.dateTime),
          status: uid_event.status,
          amount: client?.defaultSessionAmount || 0,
          meetLink: uid_event.hangoutLink,
          calenderEventId: saveEvent._id,
          _id: new Types.ObjectId(),
        };
        scheduleData.recurrenceDates.push(reccurrance);
        saveEvent.scheduleRecId = reccurrance._id;
        await saveEvent.save();
      }

      const schedule = await ScheduleDao.createSchedule(scheduleData);

      if (schedule.recurrenceDates.length == 1) {
        schedule.recurrence = "Does Not Repeat";
      } else {
        const firstDate = moment(schedule.recurrenceDates[0].fromDate);
        const secondDate = moment(schedule.recurrenceDates[1].fromDate);

        // Calculate day difference
        const dayDifference = secondDate.diff(firstDate, "days");

        // Get the day of the week for firstDate
        const dayOfWeek = firstDate.format("dddd"); // e.g., "Monday"

        if (dayDifference == 1) {
          schedule.recurrence = "Every Day";
        } else if (dayDifference == 7) {
          schedule.recurrence = `Every Week On ${dayOfWeek}`;
        } else {
          schedule.recurrence = `Every Two Weeks ${dayOfWeek}`;
        }
      }
      schedule.save();
      // Create PayTrackers for each recurrence
      for (let recurrence of schedule.recurrenceDates) {
        const paymentTracker: any = await PayTrackerService.createPayTracker({
          therapistId: therapist._id,
          scheduleId: schedule._id, // Use the created schedule's ID
          scheduleRecId: recurrence._id, // Use the recurrence ID
          clientId: client._id,
          dueDate: recurrence.fromDate || "",
          amount: {
            currency: "INR",
            value: recurrence.amount,
          },
          paymentType: moment(recurrence.fromDate).isBefore(moment())
            ? PaymentTrackerTypeEnum.Advance
            : PaymentTrackerTypeEnum.Post_Session,
          status: PaymentTrackerStatusEnum.Still_Pending,
          paymentDate: undefined,
          isDeleted: false,
          tags: [],
          sendRemainder: 0,
          isFine: false,
          cancellationFee: {
            currency: "INR",
            value: 0,
          },
        });

        // console.log(paymentTracker);

        if (!paymentTracker) {
          return throwError("Payment Tracker Not Created.", 500);
        }

        await calendarEventModel.findByIdAndUpdate(
          recurrence.calenderEventId,
          {
            $set: {
              location: "online",
              scheduleId: schedule._id,
            },
          },
          {
            new: true,
          }
        );

        recurrence.payTrackerId = paymentTracker._id;
      }
      schedule.save();
      schedules.push(schedule);

      let htmlTemplate = createScheduleTemplate({
        clientName: schedule.name || "There",
        therapistName: therapist.name || "",
        scheduleDate: schedule?.recurrenceDates[0]?.fromDate || "",
        meetingLink: schedule?.recurrenceDates[0]?.meetLink || "",
        // paymentLink: "",
        // payLater: true,
        timezone: timeZone,
        amount: schedule?.recurrenceDates[0]?.amount || 0,
      });

      let senderData = {
        email: therapist.email,
        name: therapist.name,
      };

      let receiverData = [
        {
          email: email,
          name: name || "There",
        },
      ];

      let subject = MailSubjectEnum.REMAINDER;

      // Send email notification
      await Mailer2.sendMail(senderData, receiverData, subject, htmlTemplate);
    }

    return {
      schedules: schedules,
    };
  }

  static async syncCalendarClientsLocally(
    therapistId: string,
    events: { calendarEventId: string; summary: string; attendee: string }[]
  ) {
    const googleCalendarData = await GoogleCalendarService.findByTherapist(
      therapistId
    );
    if (!googleCalendarData) {
      return throwError("No Google Calendar Data found.", 404);
    }

    const therapist = await TherapistDao.getTherapist(therapistId);
    if (!therapist) {
      return throwError("No Therapist found.", 404);
    }

    let clientsWithEvents = [];
    let inactiveClients: string[] = [];

    // **First, check all clients**
    for (let event of events) {
      const clientExist = await ClientService.getClientByEmail(
        event.attendee,
        therapistId
      );

      if (clientExist) {
        if (clientExist.isActive === false) {
          inactiveClients.push(event.attendee);
        }

        clientsWithEvents.push({
          calendarEventId: event.calendarEventId,
          summary: event.summary,
          attendee: event.attendee,
          exists: true,
          client: {
            id: clientExist._id,
            name: clientExist.name,
            mobileNumber: clientExist.phone,
            Amount: clientExist.defaultSessionAmount,
            isActive: clientExist.isActive,
          },
        });
      } else {
        clientsWithEvents.push({
          calendarEventId: event.calendarEventId,
          summary: event.summary,
          attendee: event.attendee,
          exists: false,
          client: null,
        });
      }
    }

    // **If any client is inactive, stop syncing and return error**
    if (inactiveClients.length > 0) {
      return {
        success: false,
        message: `Please remove Inactive
          clients sessions to sync.
          ${inactiveClients.join("\n")}`,
        inactiveClients,
      };
    }

    //Sort out clients so that client that do not exist show first.
    clientsWithEvents.sort((a, b) => Number(a.exists) - Number(b.exists));
    // **Check if all clients exist before syncing**
    const syncable = clientsWithEvents.every((event) => event.exists);

    return {
      success: true,
      syncable: syncable,
      events: clientsWithEvents,
    };
  }

  static async resyncCalendarEvents(therapistId: any, maxResults?: number) {
    const googleCalendarData = await GoogleCalendarService.findByTherapist(
      therapistId
    );

    if (!googleCalendarData) {
      throwError("No Google Calender Data found.", 404);
      return {
        syncable: [],
        not_syncable: [],
        rescheduleEvents: [],
      };
    }
    const oauth2Client = new google.auth.OAuth2(
      CONFIG.clientId,
      CONFIG.clientSecret,
      CONFIG.clientRedirectUrl
    );

    const tokens = {
      access_token: googleCalendarData.access_token,
      refresh_token: googleCalendarData.refresh_token, // you'll only get this once, so store it securely
    };

    if (!tokens) {
      throwError("No Google Calender Data found.", 404);
      return {
        syncable: [],
        not_syncable: [],
        rescheduleEvents: [],
      };
    }

    oauth2Client.setCredentials(tokens);
    const calendar = google.calendar({ version: "v3", auth: oauth2Client });

    const calenderEventsParams: any = {
      calendarId: "primary",
      timeMin: new Date().toISOString(),
      maxResults: maxResults, // Change this value if you want to fetch more or fewer events
      singleEvents: true,
      orderBy: "startTime",
    };

    if (maxResults) {
      calenderEventsParams["maxResults"] = maxResults; // Change this value if you want to fetch more or fewer events
    } else {
      calenderEventsParams["timeMax"] = moment().add(3, "months").toISOString();
    }

    const calendarResponse = await calendar.events.list(calenderEventsParams);

    const events = calendarResponse.data.items;

    if (!events || events.length == 0) {
      return {
        syncable: [],
        not_syncable: [],
        rescheduleEvents: [],
      };
    }

    let event_ids = events.map((event) => event.id);
    let event_ids_unique = Array.from(new Set(event_ids));

    // Get existing events from database
    let existing_events = await CalendarEventDao.getTherapistCalendarEvents(
      therapistId
    );

    let existing_event_ids = existing_events.map((event) => event.id);

    // Detect rescheduled events first
    const rescheduleEvents = await this.detectRescheduledEvents(
      therapistId,
      events,
      existing_events
    );

    // Get IDs of rescheduled events to exclude from syncable
    const rescheduledEventIds = rescheduleEvents.flatMap((event: any) =>
      event.eventIds || [event.id]
    );

    let not_existing_ids = event_ids_unique.filter(
      (id: any) => !existing_event_ids.includes(id) && !rescheduledEventIds.includes(id)
    );

    let all_non_synced_events: any[] = [];

    for (let not_existing_id of not_existing_ids) {
      const event_details = events.find((event) => event.id == not_existing_id);

      if (!event_details) {
        continue;
      }

      const checkifinArray = all_non_synced_events.find(
        (eve: any) => eve.iCalUID == event_details.iCalUID
      );
      if (checkifinArray) {
        continue;
      }

      let all_not_existing_events = events.filter(
        (event) =>
          event.iCalUID == event_details.iCalUID &&
          !existing_event_ids.includes(event.id) &&
          !rescheduledEventIds.includes(event.id)
      );

      // let all_not_existing_events = events.filter((event) => event.iCalUID == not_existing_id);
      let event: any = all_not_existing_events[0];
      event["eventOccurences"] = all_not_existing_events.length;
      event["eventIds"] = all_not_existing_events.map((event) => event.id);
      all_non_synced_events.push(event);
    }

    const syncable = all_non_synced_events.filter(
      (eve) => eve.attendees && eve.attendees.length > 1
    );
    const not_syncable = all_non_synced_events.filter((eve) => !eve.attendees);

    return {
      syncable: syncable,
      not_syncable: not_syncable,
      rescheduleEvents: rescheduleEvents,
    };
  }

  /**
   * Detect rescheduled events from Google Calendar
   * This method identifies events that have been rescheduled and returns them separately
   */
  static async detectRescheduledEvents(
    therapistId: any,
    calendarEvents: any[],
    existingEvents: any[]
  ): Promise<any[]> {
    try {
      console.log(`🔍 Detecting rescheduled events for therapist ${therapistId}`);

      const rescheduledEvents: any[] = [];
      const processedICalUIDs = new Set<string>();

      // Get all existing schedules for this therapist
      const existingSchedules = await ScheduleDao.getScheduleByTherapistId(therapistId);

      for (const calendarEvent of calendarEvents) {
        // Skip if already processed this iCalUID
        if (processedICalUIDs.has(calendarEvent.iCalUID)) {
          continue;
        }

        // Check if this is a detached series event (rescheduled from Google Calendar)
        const isDetachedEvent = this.isDetachedSeriesEvent(calendarEvent.id);

        if (isDetachedEvent) {
          console.log(`🔄 Found detached event: ${calendarEvent.id}`);

          // Check if the base ID exists in database
          const baseId = calendarEvent.id.split('_')[0];
          const existsInDatabase = existingEvents.some(e => e.id === baseId);

          if (existsInDatabase) {
            // This is a rescheduled event from an existing synced session
            const allDetachedEvents = calendarEvents.filter(
              event => event.iCalUID === calendarEvent.iCalUID
            );

            const rescheduledEvent = {
              ...calendarEvent,
              eventOccurences: allDetachedEvents.length,
              eventIds: allDetachedEvents.map(event => event.id),
              isRescheduled: true,
              rescheduleType: allDetachedEvents.length > 1 ? 'series' : 'single',
              originalEventId: baseId
            };

            rescheduledEvents.push(rescheduledEvent);
            processedICalUIDs.add(calendarEvent.iCalUID);
            console.log(`✅ Added rescheduled event: ${calendarEvent.id} (type: ${rescheduledEvent.rescheduleType})`);
          } else {
            // Base ID doesn't exist in database, treat as syncable
            console.log(`❌ Base ID ${baseId} not found in database, treating as syncable`);
          }
        } else {
          // Check if this event matches an existing schedule but with different timing
          const matchingSchedule = await this.findMatchingScheduleForEvent(
            therapistId,
            calendarEvent,
            existingSchedules
          );

          if (matchingSchedule) {
            console.log(`🔄 Found timing-based rescheduled event: ${calendarEvent.id}`);

            const allEventsInSeries = calendarEvents.filter(
              event => event.iCalUID === calendarEvent.iCalUID
            );

            const rescheduledEvent = {
              ...calendarEvent,
              eventOccurences: allEventsInSeries.length,
              eventIds: allEventsInSeries.map(event => event.id),
              isRescheduled: true,
              rescheduleType: allEventsInSeries.length > 1 ? 'series' : 'single',
              matchingScheduleId: matchingSchedule._id
            };

            rescheduledEvents.push(rescheduledEvent);
            processedICalUIDs.add(calendarEvent.iCalUID);
            console.log(`✅ Added timing-based rescheduled event: ${calendarEvent.id}`);
          }
        }
      }

      console.log(`✅ Detected ${rescheduledEvents.length} rescheduled events`);
      return rescheduledEvents;

    } catch (error: any) {
      console.error(`💥 Error detecting rescheduled events:`, error.message);
      return [];
    }
  }

  /**
   * Check if event ID is a detached series event (rescheduled from Google Calendar)
   */
  static isDetachedSeriesEvent(eventId: string): boolean {
    // Check for detached ID pattern (contains underscore with timestamp)
    const detachedIdPattern = /^[a-zA-Z0-9]+_\d{8}T\d{6}Z$/;
    return detachedIdPattern.test(eventId);
  }

  /**
   * Find matching schedule for an event based on attendees and timing patterns
   */
  static async findMatchingScheduleForEvent(
    therapistId: any,
    calendarEvent: any,
    existingSchedules: any[]
  ): Promise<any | null> {
    try {
      // Get attendee email (client email)
      const attendeeEmail = calendarEvent.attendees?.find(
        (a: any) => a.email !== calendarEvent.organizer?.email
      )?.email;

      if (!attendeeEmail) {
        return null;
      }

      // Find schedules with matching email
      const matchingSchedules = existingSchedules.filter((schedule: any) =>
        schedule.email === attendeeEmail ||
        (schedule.additionalEmails && schedule.additionalEmails.includes(attendeeEmail))
      );

      if (matchingSchedules.length === 0) {
        return null;
      }

      const eventStartTime = new Date(calendarEvent.start.dateTime || calendarEvent.start.date);

      // Check if any schedule has a recurrence date that's different from this event's timing
      for (const schedule of matchingSchedules) {
        for (const recurrenceDate of schedule.recurrenceDates) {
          const scheduleStartTime = new Date(recurrenceDate.fromDate);

          // If same day but different time, it's likely rescheduled
          const isSameDay = eventStartTime.toDateString() === scheduleStartTime.toDateString();
          const timeDiff = Math.abs(eventStartTime.getTime() - scheduleStartTime.getTime());

          // If same day but time difference > 5 minutes, consider it rescheduled
          if (isSameDay && timeDiff > 300000) { // 5 minutes in milliseconds
            return schedule;
          }

          // If different day but same attendee, also consider it rescheduled
          if (!isSameDay && timeDiff > 86400000) { // 1 day in milliseconds
            return schedule;
          }
        }
      }

      return null;
    } catch (error: any) {
      console.error(`Error finding matching schedule for event:`, error.message);
      return null;
    }
  }

  static async eventByDate(
    therapistId: any,
    maxResults: number,
    startDate: any,
    endDate: any
  ) {
    let startingDate = new Date(startDate);
    let endingDate = new Date(endDate);
    const googleCalendarData = await GoogleCalendarService.findByTherapist(
      therapistId
    );
    if (!googleCalendarData) {
      throwError("No Google Calender Data found.", 404);
      return [];
    }
    const oauth2Client = new google.auth.OAuth2(
      CONFIG.clientId,
      CONFIG.clientSecret,
      CONFIG.clientRedirectUrl
    );
    const tokens = {
      access_token: googleCalendarData.access_token,
      refresh_token: googleCalendarData.refresh_token, // you'll only get this once, so store it securely
    };
    if (!tokens) {
      throwError("No Google Calender Data found.", 404);
      return {
        syncable: [],
        not_syncable: [],
      };
    }
    oauth2Client.setCredentials(tokens);
    const calendar = google.calendar({ version: "v3", auth: oauth2Client });
    const calendarResponse: any = await calendar.events.list({
      calendarId: "primary",
      timeMin: startingDate.toISOString(),
      timeMax: endingDate.toISOString(),
      maxResults: maxResults, // Change this value if you want to fetch more or fewer events
      singleEvents: true,
      orderBy: "startTime",
    });
    const events = calendarResponse?.data?.items;
    return events;
  }

  // Find events by therapist and dates to avoid duplicates
  static async findEventsByTherapistAndDates(
    therapistId: string,
    dates: { fromDate: Date; toDate: Date }[]
  ) {
    try {
      // Get Google Calendar credentials
      const googleCalendarData = await GoogleCalendarService.findByTherapist(
        therapistId
      );
      if (!googleCalendarData) {
        return [];
      }

      // Set up OAuth client for Google Calendar API
      const oauth2Client = new google.auth.OAuth2(
        CONFIG.clientId,
        CONFIG.clientSecret,
        CONFIG.clientRedirectUrl
      );
      oauth2Client.setCredentials({
        access_token: googleCalendarData.access_token,
        refresh_token: googleCalendarData.refresh_token,
      });

      const calendar = google.calendar({ version: "v3", auth: oauth2Client });

      // Find events for each date range
      const existingEvents = [];

      for (const dateRange of dates) {
        try {
          // Convert dates to ISO strings for Google Calendar API
          const timeMin = moment(dateRange.fromDate)
            .subtract(5, "minutes")
            .toISOString();
          const timeMax = moment(dateRange.toDate)
            .add(5, "minutes")
            .toISOString();

          // Search for events in this time range
          const response = await calendar.events.list({
            calendarId: "primary",
            timeMin: timeMin,
            timeMax: timeMax,
            singleEvents: true,
          });

          if (response.data.items && response.data.items.length > 0) {
            existingEvents.push(...response.data.items);
          }
        } catch (error) {
          console.error(`Error searching for events in date range:`, error);
        }
      }

      return existingEvents;
    } catch (error) {
      console.error("Error finding events by therapist and dates:", error);
      return [];
    }
  }

  static async updateEventTime(
    therapistId: any,
    calendarId: any,
    date: { fromDate: any; toDate: any }
  ) {
    const googleCalendarData = await GoogleCalendarService.findByTherapist(
      therapistId
    );
    if (!googleCalendarData) {
      return throwError("No Google Calender Data found.", 404);
    }

    const oauth2Client = new google.auth.OAuth2(
      CONFIG.clientId,
      CONFIG.clientSecret,
      CONFIG.clientRedirectUrl
    );

    const tokens = {
      access_token: googleCalendarData.access_token,
      refresh_token: googleCalendarData.refresh_token, // you'll only get this once, so store it securely
    };

    if (!tokens) {
      throwError("No Google Calender Data found.", 404);
      return {
        syncable: [],
        not_syncable: [],
      };
    }

    oauth2Client.setCredentials(tokens);

    const event = await GoogleCalendarService.findby_id(calendarId);
    if (!event) {
      return throwError("Calendar Event not found", 404);
    }
    // console.log("event attendees : ",event.attendees);
    const payload = {
      start: {
        dateTime: date.fromDate,
        timeZone: event.start.timeZone,
      },
      end: {
        dateTime: date.toDate,
        timeZone: event.end.timeZone,
      },
      summary: event.summary,
      description: event.description,
      attendees: event.attendees,
    };

    const calendar: any = google.calendar({
      version: "v3",
      auth: oauth2Client,
    });

    try {
      const eventData = await calendar.events.get({
        calendarId: "primary",
        eventId: event._id,
      });

      const newEvent = {
        ...eventData.data,
        ...payload,
      };

      // console.log(JSON.stringify(newEvent), "new Event");

      const response = await calendar.events.update({
        calendarId: "primary",
        eventId: event.id,
        resource: newEvent,
      });
      // console.log("Response ",response.data);
      event.id = response.data.id;
      event.start = response.data.start;
      event.end = response.data.end;

      return await event.save();
    } catch (error) {
      console.log(error);
      return throwError("Unable to update google calendar", 400);
    }
  }

  static async isTimeSlotBusy(
    therapistId: any,
    date: string, // DD-MM-YYYY
    timeSlots: { startTime: string; endTime: string; duration: number }[]
  ) {
    const googleCalendarData = await GoogleCalendarService.findByTherapist(
      therapistId
    );

    if (!googleCalendarData) {
      return {
        status: false,
        statusCode: 404,
        message: "No Google Calendar data found.",
      };
    }

    const oauth2Client = new google.auth.OAuth2(
      CONFIG.clientId,
      CONFIG.clientSecret,
      CONFIG.clientRedirectUrl
    );

    const tokens = {
      access_token: googleCalendarData.access_token,
      refresh_token: googleCalendarData.refresh_token,
    };

    if (!tokens.access_token || !tokens.refresh_token) {
      return {
        status: false,
        statusCode: 404,
        message: "Google Calendar tokens are missing.",
      };
    }

    oauth2Client.setCredentials(tokens);

    const calendar = google.calendar({ version: "v3", auth: oauth2Client });

    const busySlots: any[] = [];

    for (const slot of timeSlots) {
      // Validate slot times
      if (!slot.startTime || !slot.endTime) {
        continue;
      }

      // Parse the date in DD-MM-YYYY format
      const [day, month, year] = date.split("-");
      const formattedDate = `${year}-${month}-${day}`;

      // Combine date with time and create ISO string with correct timezone
      const startDateTime = moment.tz(
        `${formattedDate} ${slot.startTime}`,
        "YYYY-MM-DD HH:mm",
        "Asia/Kolkata"
      );

      const endDateTime = moment.tz(
        `${formattedDate} ${slot.endTime}`,
        "YYYY-MM-DD HH:mm",
        "Asia/Kolkata"
      );

      try {
        const events: any = await calendar.events.list({
          calendarId: "primary",
          timeMin: startDateTime.toISOString(),
          timeMax: endDateTime.toISOString(),
          singleEvents: true,
          orderBy: "startTime",
          timeZone: "Asia/Kolkata",
        });

        if (events.data.items && events.data.items.length > 0) {
          busySlots.push({
            slot,
            conflictingEvent: {
              eventStart: events.data.items[0].start.dateTime,
              eventEnd: events.data.items[0].end.dateTime,
            },
          });
        }
      } catch (error) {
        console.error("Error checking slot:", slot, error);
        return {
          status: false,
          statusCode: 500,
          message:
            "Failed while checking Google Calendar for one or more slots.",
          error,
        };
      }
    }

    if (busySlots.length > 0) {
      return {
        status: true,
        busy: true,
        message: "Some time slots are busy.",
        busySlots,
      };
    }

    return {
      status: true,
      busy: false,
      message: "All time slots are free.",
    };
  }
}
