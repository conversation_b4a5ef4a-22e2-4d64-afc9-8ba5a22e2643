/**
 * Enhanced Resync Service
 * 
 * This service provides enhanced calendar resync functionality with:
 * - Reliable reschedule detection
 * - Separation of syncable, not_syncable, and rescheduleEvents
 * - Support for both detached series events and timing-based detection
 * - No assumptions or risky operations
 */

import { CalendarEventDao } from "../lib/dao/calendarEvent.dao";
import { ScheduleDao } from "../lib/dao/schedule.dao";
import { GoogleCalendarService } from "./googleCalendar.service";
import moment from "moment";

export interface IEnhancedResyncResult {
  syncable: any[];
  not_syncable: any[];
  rescheduleEvents: any[];
}

export class EnhancedResyncService {
  
  /**
   * Enhanced resync with reschedule detection
   */
  static async resyncCalendarEventsWithRescheduleDetection(
    therapistId: any,
    maxResults?: number
  ): Promise<IEnhancedResyncResult> {
    try {
      console.log(`🔄 Starting enhanced resync for therapist ${therapistId}`);

      // Get all calendar events from Google Calendar first
      const allCalendarEvents = await EnhancedResyncService.getAllCalendarEvents(therapistId, maxResults);

      if (!allCalendarEvents || allCalendarEvents.length === 0) {
        return {
          syncable: [],
          not_syncable: [],
          rescheduleEvents: []
        };
      }

      // Get existing events from database
      const existingEvents = await CalendarEventDao.getTherapistCalendarEvents(therapistId);

      // Detect rescheduled events FIRST
      const rescheduleEvents = await EnhancedResyncService.detectRescheduledEvents(
        therapistId,
        allCalendarEvents,
        existingEvents
      );

      // Get rescheduled event IDs to exclude from basic resync
      const rescheduledEventIds = rescheduleEvents.flatMap((event: any) =>
        event.eventIds || [event.id]
      );

      console.log(`🔍 Found ${rescheduleEvents.length} rescheduled events with IDs:`, rescheduledEventIds);

      // Now process the remaining events using our own logic (similar to basic resync but excluding rescheduled)
      const { syncable, not_syncable } = await EnhancedResyncService.processNonRescheduledEvents(
        therapistId,
        allCalendarEvents,
        existingEvents,
        rescheduledEventIds
      );

      console.log(`✅ Enhanced resync completed: ${syncable.length} syncable, ${not_syncable.length} not_syncable, ${rescheduleEvents.length} rescheduled`);

      return {
        syncable: syncable,
        not_syncable: not_syncable,
        rescheduleEvents: rescheduleEvents
      };

    } catch (error: any) {
      console.error(`💥 Error in enhanced resync:`, error.message);

      // Fallback to basic resync without reschedule detection
      const basicResync = await GoogleCalendarService.resyncCalendarEvents(therapistId, maxResults);
      return {
        syncable: basicResync.syncable || [],
        not_syncable: basicResync.not_syncable || [],
        rescheduleEvents: []
      };
    }
  }

  /**
   * Process non-rescheduled events to separate into syncable and not_syncable
   */
  private static async processNonRescheduledEvents(
    therapistId: any,
    allCalendarEvents: any[],
    existingEvents: any[],
    rescheduledEventIds: string[]
  ): Promise<{ syncable: any[], not_syncable: any[] }> {
    try {
      const event_ids = allCalendarEvents.map((event) => event.id);
      const event_ids_unique = Array.from(new Set(event_ids));
      const existing_event_ids = existingEvents.map((event) => event.id);

      // Filter out existing events and rescheduled events
      const not_existing_ids = event_ids_unique.filter(
        (id: any) => !existing_event_ids.includes(id) && !rescheduledEventIds.includes(id)
      );

      console.log(`📊 Processing ${not_existing_ids.length} non-existing, non-rescheduled events`);

      const all_non_synced_events: any[] = [];

      for (let not_existing_id of not_existing_ids) {
        const event_details = allCalendarEvents.find((event) => event.id == not_existing_id);

        if (!event_details) {
          continue;
        }

        const checkifinArray = all_non_synced_events.find(
          (eve: any) => eve.iCalUID == event_details.iCalUID
        );
        if (checkifinArray) {
          continue;
        }

        let all_not_existing_events = allCalendarEvents.filter(
          (event) =>
            event.iCalUID == event_details.iCalUID &&
            !existing_event_ids.includes(event.id) &&
            !rescheduledEventIds.includes(event.id)
        );

        let event: any = all_not_existing_events[0];
        event["eventOccurences"] = all_not_existing_events.length;
        event["eventIds"] = all_not_existing_events.map((event) => event.id);
        all_non_synced_events.push(event);
      }

      const syncable = all_non_synced_events.filter(
        (eve) => eve.attendees && eve.attendees.length > 1
      );
      const not_syncable = all_non_synced_events.filter((eve) => !eve.attendees);

      return { syncable, not_syncable };

    } catch (error: any) {
      console.error(`Error processing non-rescheduled events:`, error.message);
      return { syncable: [], not_syncable: [] };
    }
  }

  /**
   * Get all calendar events from Google Calendar
   */
  private static async getAllCalendarEvents(therapistId: any, maxResults?: number): Promise<any[]> {
    try {
      const googleCalendarData = await GoogleCalendarService.findByTherapist(therapistId);
      
      if (!googleCalendarData) {
        return [];
      }

      const { google } = require("googleapis");
      const { CONFIG } = require("../config/environment");

      const oauth2Client = new google.auth.OAuth2(
        CONFIG.clientId,
        CONFIG.clientSecret,
        CONFIG.clientRedirectUrl
      );

      oauth2Client.setCredentials({
        access_token: googleCalendarData.access_token,
        refresh_token: googleCalendarData.refresh_token,
      });

      const calendar = google.calendar({ version: "v3", auth: oauth2Client });

      const calenderEventsParams: any = {
        calendarId: "primary",
        timeMin: new Date().toISOString(),
        maxResults: maxResults || 250,
        singleEvents: true,
        orderBy: "startTime",
      };

      if (!maxResults) {
        calenderEventsParams["timeMax"] = moment().add(3, "months").toISOString();
      }

      const calendarResponse = await calendar.events.list(calenderEventsParams);
      return calendarResponse.data.items || [];

    } catch (error: any) {
      console.error(`Error getting calendar events:`, error.message);
      return [];
    }
  }

  /**
   * Detect rescheduled events from Google Calendar
   * This method identifies events that have been rescheduled and returns them separately
   */
  static async detectRescheduledEvents(
    therapistId: any,
    calendarEvents: any[],
    existingEvents: any[]
  ): Promise<any[]> {
    try {
      console.log(`🔍 Detecting rescheduled events for therapist ${therapistId}`);
      console.log(`📊 Total calendar events to check: ${calendarEvents.length}`);
      console.log(`📊 Total existing events in database: ${existingEvents.length}`);

      const rescheduledEvents: any[] = [];
      const processedICalUIDs = new Set<string>();

      // Get all existing schedules for this therapist
      const existingSchedules = await ScheduleDao.getScheduleByTherapistId(therapistId);
      console.log(`📊 Total existing schedules: ${existingSchedules.length}`);

      for (const calendarEvent of calendarEvents) {
        console.log(`\n🔍 Processing event: ${calendarEvent.id} (${calendarEvent.summary})`);

        // Skip if already processed this iCalUID
        if (processedICalUIDs.has(calendarEvent.iCalUID)) {
          console.log(`⏭️ Skipping - already processed iCalUID: ${calendarEvent.iCalUID}`);
          continue;
        }

        // Check if this is a detached series event (rescheduled from Google Calendar)
        const isDetachedEvent = EnhancedResyncService.isDetachedSeriesEvent(calendarEvent.id);
        console.log(`🔍 Is detached event: ${isDetachedEvent}`);
        
        if (isDetachedEvent) {
          console.log(`🔄 Found detached event: ${calendarEvent.id}`);

          // Check if the base ID exists in database
          const baseId = calendarEvent.id.split('_')[0];
          console.log(`   Checking for base ID: ${baseId} in database`);
          console.log(`   Existing event IDs in database:`, existingEvents.map(e => e.id).slice(0, 5), '...');

          const existsInDatabase = existingEvents.some(e => e.id === baseId);

          if (existsInDatabase) {
            console.log(`✅ Base ID ${baseId} found in database - this is a rescheduled event`);

            // This is a rescheduled event from an existing synced session
            const allDetachedEvents = calendarEvents.filter(
              event => event.iCalUID === calendarEvent.iCalUID
            );

            const rescheduledEvent = {
              ...calendarEvent,
              eventOccurences: allDetachedEvents.length,
              eventIds: allDetachedEvents.map(event => event.id),
              isRescheduled: true,
              rescheduleType: allDetachedEvents.length > 1 ? 'series' : 'single',
              originalEventId: baseId
            };

            rescheduledEvents.push(rescheduledEvent);
            processedICalUIDs.add(calendarEvent.iCalUID);
            console.log(`✅ Added rescheduled event: ${calendarEvent.id} (type: ${rescheduledEvent.rescheduleType})`);
          } else {
            console.log(`❌ Base ID ${baseId} not found in database`);

            // Additional check: look for events with similar iCalUID or attendees
            const similarEvent = await EnhancedResyncService.findSimilarEventInDatabase(calendarEvent, existingEvents, existingSchedules);

            if (similarEvent) {
              console.log(`✅ Found similar event in database - treating as rescheduled`);

              const allDetachedEvents = calendarEvents.filter(
                event => event.iCalUID === calendarEvent.iCalUID
              );

              const rescheduledEvent = {
                ...calendarEvent,
                eventOccurences: allDetachedEvents.length,
                eventIds: allDetachedEvents.map(event => event.id),
                isRescheduled: true,
                rescheduleType: allDetachedEvents.length > 1 ? 'series' : 'single',
                originalEventId: baseId,
                similarEventId: similarEvent.id
              };

              rescheduledEvents.push(rescheduledEvent);
              processedICalUIDs.add(calendarEvent.iCalUID);
              console.log(`✅ Added rescheduled event based on similarity: ${calendarEvent.id}`);
            } else {
              console.log(`❌ No similar event found - treating as syncable`);
            }
          }
        } else {
          // Check if this event matches an existing schedule but with different timing
          const matchingSchedule = await EnhancedResyncService.findMatchingScheduleForEvent(
            therapistId,
            calendarEvent,
            existingSchedules
          );
          
          if (matchingSchedule) {
            console.log(`🔄 Found timing-based rescheduled event: ${calendarEvent.id}`);
            
            const allEventsInSeries = calendarEvents.filter(
              event => event.iCalUID === calendarEvent.iCalUID
            );
            
            const rescheduledEvent = {
              ...calendarEvent,
              eventOccurences: allEventsInSeries.length,
              eventIds: allEventsInSeries.map(event => event.id),
              isRescheduled: true,
              rescheduleType: allEventsInSeries.length > 1 ? 'series' : 'single',
              matchingScheduleId: matchingSchedule._id
            };
            
            rescheduledEvents.push(rescheduledEvent);
            processedICalUIDs.add(calendarEvent.iCalUID);
            console.log(`✅ Added timing-based rescheduled event: ${calendarEvent.id}`);
          }
        }
      }

      console.log(`\n📊 DETECTION SUMMARY:`);
      console.log(`   Total events processed: ${calendarEvents.length}`);
      console.log(`   Rescheduled events found: ${rescheduledEvents.length}`);
      console.log(`   Rescheduled event IDs:`, rescheduledEvents.map(e => e.id));

      return rescheduledEvents;

    } catch (error: any) {
      console.error(`💥 Error detecting rescheduled events:`, error.message);
      return [];
    }
  }

  /**
   * Check if event ID is a detached series event (rescheduled from Google Calendar)
   */
  static isDetachedSeriesEvent(eventId: string): boolean {
    // Check for detached ID pattern (contains underscore with timestamp)
    const detachedIdPattern = /^[a-zA-Z0-9]+_\d{8}T\d{6}Z$/;
    return detachedIdPattern.test(eventId);
  }

  /**
   * Find similar event in database based on attendees and summary
   */
  static async findSimilarEventInDatabase(
    calendarEvent: any,
    existingEvents: any[],
    existingSchedules: any[]
  ): Promise<any | null> {
    try {
      // Get attendee email (client email)
      const attendeeEmail = calendarEvent.attendees?.find(
        (a: any) => a.email !== calendarEvent.organizer?.email
      )?.email;

      if (!attendeeEmail) {
        return null;
      }

      // Look for existing events with same attendee email
      for (const existingEvent of existingEvents) {
        // Check if this existing event has the same attendee
        if (existingEvent.attendees) {
          const hasMatchingAttendee = existingEvent.attendees.some((attendee: any) =>
            attendee.email === attendeeEmail
          );

          if (hasMatchingAttendee) {
            // Additional check: similar summary
            if (calendarEvent.summary && existingEvent.summary) {
              const summaryMatch = calendarEvent.summary.toLowerCase().includes(existingEvent.summary.toLowerCase()) ||
                                  existingEvent.summary.toLowerCase().includes(calendarEvent.summary.toLowerCase());
              if (summaryMatch) {
                return existingEvent;
              }
            } else {
              // If no summary to compare, just attendee match is enough
              return existingEvent;
            }
          }
        }
      }

      // Also check schedules for matching email
      const matchingSchedules = existingSchedules.filter((schedule: any) =>
        schedule.email === attendeeEmail ||
        (schedule.additionalEmails && schedule.additionalEmails.includes(attendeeEmail))
      );

      if (matchingSchedules.length > 0) {
        // Return the first matching schedule as a reference
        return { id: matchingSchedules[0]._id, type: 'schedule' };
      }

      return null;
    } catch (error: any) {
      console.error(`Error finding similar event in database:`, error.message);
      return null;
    }
  }

  /**
   * Find matching schedule for an event based on attendees and timing patterns
   */
  static async findMatchingScheduleForEvent(
    therapistId: any,
    calendarEvent: any,
    existingSchedules: any[]
  ): Promise<any | null> {
    try {
      // Get attendee email (client email)
      const attendeeEmail = calendarEvent.attendees?.find(
        (a: any) => a.email !== calendarEvent.organizer?.email
      )?.email;
      
      if (!attendeeEmail) {
        return null;
      }

      // Find schedules with matching email
      const matchingSchedules = existingSchedules.filter((schedule: any) =>
        schedule.email === attendeeEmail || 
        (schedule.additionalEmails && schedule.additionalEmails.includes(attendeeEmail))
      );

      if (matchingSchedules.length === 0) {
        return null;
      }

      const eventStartTime = new Date(calendarEvent.start.dateTime || calendarEvent.start.date);
      
      // Check if any schedule has a recurrence date that's different from this event's timing
      for (const schedule of matchingSchedules) {
        for (const recurrenceDate of schedule.recurrenceDates) {
          const scheduleStartTime = new Date(recurrenceDate.fromDate);
          
          // If same day but different time, it's likely rescheduled
          const isSameDay = eventStartTime.toDateString() === scheduleStartTime.toDateString();
          const timeDiff = Math.abs(eventStartTime.getTime() - scheduleStartTime.getTime());
          
          // If same day but time difference > 5 minutes, consider it rescheduled
          if (isSameDay && timeDiff > 300000) { // 5 minutes in milliseconds
            return schedule;
          }
          
          // If different day but same attendee, also consider it rescheduled
          if (!isSameDay && timeDiff > 86400000) { // 1 day in milliseconds
            return schedule;
          }
        }
      }

      return null;
    } catch (error: any) {
      console.error(`Error finding matching schedule for event:`, error.message);
      return null;
    }
  }
}
