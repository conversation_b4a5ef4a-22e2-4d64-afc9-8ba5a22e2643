/**
 * Enhanced Resync Service
 * 
 * This service provides enhanced calendar resync functionality with:
 * - Reliable reschedule detection
 * - Separation of syncable, not_syncable, and rescheduleEvents
 * - Support for both detached series events and timing-based detection
 * - No assumptions or risky operations
 */

import { CalendarEventDao } from "../lib/dao/calendarEvent.dao";
import { ScheduleDao } from "../lib/dao/schedule.dao";
import { GoogleCalendarService } from "./googleCalendar.service";
import moment from "moment";

export interface IEnhancedResyncResult {
  syncable: any[];
  not_syncable: any[];
  rescheduleEvents: any[];
}

export class EnhancedResyncService {
  
  /**
   * Enhanced resync with reschedule detection
   */
  static async resyncCalendarEventsWithRescheduleDetection(
    therapistId: any, 
    maxResults?: number
  ): Promise<IEnhancedResyncResult> {
    try {
      console.log(`🔄 Starting enhanced resync for therapist ${therapistId}`);
      
      // Get the basic resync data from existing service
      const basicResync = await GoogleCalendarService.resyncCalendarEvents(therapistId, maxResults);
      
      if (!basicResync || (!basicResync.syncable && !basicResync.not_syncable)) {
        return {
          syncable: [],
          not_syncable: [],
          rescheduleEvents: []
        };
      }

      // Get all calendar events from Google Calendar for reschedule detection
      const allCalendarEvents = await this.getAllCalendarEvents(therapistId, maxResults);
      
      if (!allCalendarEvents || allCalendarEvents.length === 0) {
        return {
          syncable: basicResync.syncable || [],
          not_syncable: basicResync.not_syncable || [],
          rescheduleEvents: []
        };
      }

      // Get existing events from database
      const existingEvents = await CalendarEventDao.getTherapistCalendarEvents(therapistId);
      
      // Detect rescheduled events
      const rescheduleEvents = await this.detectRescheduledEvents(
        therapistId,
        allCalendarEvents,
        existingEvents
      );

      // Filter out rescheduled events from syncable and not_syncable
      const rescheduledEventIds = rescheduleEvents.flatMap((event: any) => 
        event.eventIds || [event.id]
      );

      const filteredSyncable = (basicResync.syncable || []).filter((event: any) => {
        const eventIds = event.eventIds || [event.id];
        return !eventIds.some((id: string) => rescheduledEventIds.includes(id));
      });

      const filteredNotSyncable = (basicResync.not_syncable || []).filter((event: any) => {
        const eventIds = event.eventIds || [event.id];
        return !eventIds.some((id: string) => rescheduledEventIds.includes(id));
      });

      console.log(`✅ Enhanced resync completed: ${filteredSyncable.length} syncable, ${filteredNotSyncable.length} not_syncable, ${rescheduleEvents.length} rescheduled`);

      return {
        syncable: filteredSyncable,
        not_syncable: filteredNotSyncable,
        rescheduleEvents: rescheduleEvents
      };

    } catch (error: any) {
      console.error(`💥 Error in enhanced resync:`, error.message);
      
      // Fallback to basic resync without reschedule detection
      const basicResync = await GoogleCalendarService.resyncCalendarEvents(therapistId, maxResults);
      return {
        syncable: basicResync.syncable || [],
        not_syncable: basicResync.not_syncable || [],
        rescheduleEvents: []
      };
    }
  }

  /**
   * Get all calendar events from Google Calendar
   */
  private static async getAllCalendarEvents(therapistId: any, maxResults?: number): Promise<any[]> {
    try {
      const googleCalendarData = await GoogleCalendarService.findByTherapist(therapistId);
      
      if (!googleCalendarData) {
        return [];
      }

      const { google } = require("googleapis");
      const { CONFIG } = require("../config/environment");

      const oauth2Client = new google.auth.OAuth2(
        CONFIG.clientId,
        CONFIG.clientSecret,
        CONFIG.clientRedirectUrl
      );

      oauth2Client.setCredentials({
        access_token: googleCalendarData.access_token,
        refresh_token: googleCalendarData.refresh_token,
      });

      const calendar = google.calendar({ version: "v3", auth: oauth2Client });

      const calenderEventsParams: any = {
        calendarId: "primary",
        timeMin: new Date().toISOString(),
        maxResults: maxResults || 250,
        singleEvents: true,
        orderBy: "startTime",
      };

      if (!maxResults) {
        calenderEventsParams["timeMax"] = moment().add(3, "months").toISOString();
      }

      const calendarResponse = await calendar.events.list(calenderEventsParams);
      return calendarResponse.data.items || [];

    } catch (error: any) {
      console.error(`Error getting calendar events:`, error.message);
      return [];
    }
  }

  /**
   * Detect rescheduled events from Google Calendar
   * This method identifies events that have been rescheduled and returns them separately
   */
  static async detectRescheduledEvents(
    therapistId: any,
    calendarEvents: any[],
    existingEvents: any[]
  ): Promise<any[]> {
    try {
      console.log(`🔍 Detecting rescheduled events for therapist ${therapistId}`);
      
      const rescheduledEvents: any[] = [];
      const processedICalUIDs = new Set<string>();

      // Get all existing schedules for this therapist
      const existingSchedules = await ScheduleDao.getScheduleByTherapistId(therapistId);

      for (const calendarEvent of calendarEvents) {
        // Skip if already processed this iCalUID
        if (processedICalUIDs.has(calendarEvent.iCalUID)) {
          continue;
        }

        // Check if this is a detached series event (rescheduled from Google Calendar)
        const isDetachedEvent = this.isDetachedSeriesEvent(calendarEvent.id);
        
        if (isDetachedEvent) {
          console.log(`🔄 Found detached event: ${calendarEvent.id}`);
          
          // Check if the base ID exists in database
          const baseId = calendarEvent.id.split('_')[0];
          const existsInDatabase = existingEvents.some(e => e.id === baseId);
          
          if (existsInDatabase) {
            // This is a rescheduled event from an existing synced session
            const allDetachedEvents = calendarEvents.filter(
              event => event.iCalUID === calendarEvent.iCalUID
            );
            
            const rescheduledEvent = {
              ...calendarEvent,
              eventOccurences: allDetachedEvents.length,
              eventIds: allDetachedEvents.map(event => event.id),
              isRescheduled: true,
              rescheduleType: allDetachedEvents.length > 1 ? 'series' : 'single',
              originalEventId: baseId
            };
            
            rescheduledEvents.push(rescheduledEvent);
            processedICalUIDs.add(calendarEvent.iCalUID);
            console.log(`✅ Added rescheduled event: ${calendarEvent.id} (type: ${rescheduledEvent.rescheduleType})`);
          } else {
            // Base ID doesn't exist in database, treat as syncable
            console.log(`❌ Base ID ${baseId} not found in database, treating as syncable`);
          }
        } else {
          // Check if this event matches an existing schedule but with different timing
          const matchingSchedule = await this.findMatchingScheduleForEvent(
            therapistId,
            calendarEvent,
            existingSchedules
          );
          
          if (matchingSchedule) {
            console.log(`🔄 Found timing-based rescheduled event: ${calendarEvent.id}`);
            
            const allEventsInSeries = calendarEvents.filter(
              event => event.iCalUID === calendarEvent.iCalUID
            );
            
            const rescheduledEvent = {
              ...calendarEvent,
              eventOccurences: allEventsInSeries.length,
              eventIds: allEventsInSeries.map(event => event.id),
              isRescheduled: true,
              rescheduleType: allEventsInSeries.length > 1 ? 'series' : 'single',
              matchingScheduleId: matchingSchedule._id
            };
            
            rescheduledEvents.push(rescheduledEvent);
            processedICalUIDs.add(calendarEvent.iCalUID);
            console.log(`✅ Added timing-based rescheduled event: ${calendarEvent.id}`);
          }
        }
      }

      console.log(`✅ Detected ${rescheduledEvents.length} rescheduled events`);
      return rescheduledEvents;

    } catch (error: any) {
      console.error(`💥 Error detecting rescheduled events:`, error.message);
      return [];
    }
  }

  /**
   * Check if event ID is a detached series event (rescheduled from Google Calendar)
   */
  static isDetachedSeriesEvent(eventId: string): boolean {
    // Check for detached ID pattern (contains underscore with timestamp)
    const detachedIdPattern = /^[a-zA-Z0-9]+_\d{8}T\d{6}Z$/;
    return detachedIdPattern.test(eventId);
  }

  /**
   * Find matching schedule for an event based on attendees and timing patterns
   */
  static async findMatchingScheduleForEvent(
    therapistId: any,
    calendarEvent: any,
    existingSchedules: any[]
  ): Promise<any | null> {
    try {
      // Get attendee email (client email)
      const attendeeEmail = calendarEvent.attendees?.find(
        (a: any) => a.email !== calendarEvent.organizer?.email
      )?.email;
      
      if (!attendeeEmail) {
        return null;
      }

      // Find schedules with matching email
      const matchingSchedules = existingSchedules.filter((schedule: any) =>
        schedule.email === attendeeEmail || 
        (schedule.additionalEmails && schedule.additionalEmails.includes(attendeeEmail))
      );

      if (matchingSchedules.length === 0) {
        return null;
      }

      const eventStartTime = new Date(calendarEvent.start.dateTime || calendarEvent.start.date);
      
      // Check if any schedule has a recurrence date that's different from this event's timing
      for (const schedule of matchingSchedules) {
        for (const recurrenceDate of schedule.recurrenceDates) {
          const scheduleStartTime = new Date(recurrenceDate.fromDate);
          
          // If same day but different time, it's likely rescheduled
          const isSameDay = eventStartTime.toDateString() === scheduleStartTime.toDateString();
          const timeDiff = Math.abs(eventStartTime.getTime() - scheduleStartTime.getTime());
          
          // If same day but time difference > 5 minutes, consider it rescheduled
          if (isSameDay && timeDiff > 300000) { // 5 minutes in milliseconds
            return schedule;
          }
          
          // If different day but same attendee, also consider it rescheduled
          if (!isSameDay && timeDiff > 86400000) { // 1 day in milliseconds
            return schedule;
          }
        }
      }

      return null;
    } catch (error: any) {
      console.error(`Error finding matching schedule for event:`, error.message);
      return null;
    }
  }
}
